import google.generativeai as genai
import logging
from config import Config

class GeminiService:
    def __init__(self):
        genai.configure(api_key=Config.GEMINI_API_KEY)
        self.model = genai.GenerativeModel(Config.GEMINI_MODEL)
        self.conversation_history = []
        logging.info("Gemini service initialized")
    
    def get_response(self, user_input):
        """
        Get response from Gemini
        
        Args:
            user_input (str): User's message
            
        Returns:
            str: <PERSON>'s response or error message
        """
        try:
            # Add user message to conversation history
            self.conversation_history.append({"role": "user", "content": user_input})
            
            # Keep conversation history manageable (last 10 messages)
            if len(self.conversation_history) > 10:
                self.conversation_history = self.conversation_history[-10:]
            
            # Build conversation context for Gemini
            conversation_text = ""
            for message in self.conversation_history:
                if message["role"] == "user":
                    conversation_text += f"User: {message['content']}\n"
                else:
                    conversation_text += f"Assistant: {message['content']}\n"
            
            # Generate response using Gemini
            response = self.model.generate_content(
                conversation_text,
                generation_config=genai.types.GenerationConfig(
                    max_output_tokens=Config.MAX_TOKENS,
                    temperature=Config.TEMPERATURE,
                )
            )
            
            # Extract response text
            assistant_response = response.text.strip()
            
            # Add assistant response to conversation history
            self.conversation_history.append({"role": "assistant", "content": assistant_response})
            
            logging.info(f"Gemini response: {assistant_response}")
            return assistant_response
            
        except Exception as e:
            error_msg = f"Error getting Gemini response: {e}"
            logging.error(error_msg)
            return error_msg
    
    def clear_conversation(self):
        """Clear conversation history"""
        self.conversation_history = []
        logging.info("Conversation history cleared")
    
    def get_conversation_history(self):
        """Get current conversation history"""
        return self.conversation_history.copy()
