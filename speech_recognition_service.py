import speech_recognition as sr
import logging
from config import Config

class SpeechRecognitionService:
    def __init__(self):
        self.recognizer = sr.Recognizer()
        self.microphone = sr.Microphone()
        
        # Adjust for ambient noise
        with self.microphone as source:
            self.recognizer.adjust_for_ambient_noise(source)
        
        logging.info("Speech recognition service initialized")
    
    def listen_for_speech(self, timeout=None, phrase_timeout=None):
        """
        Listen for speech from the microphone
        
        Args:
            timeout: Maximum time to wait for speech to start
            phrase_timeout: Maximum time to wait for phrase to complete
            
        Returns:
            str: Recognized text or None if no speech detected
        """
        try:
            with self.microphone as source:
                logging.info("Listening for speech...")
                audio = self.recognizer.listen(
                    source, 
                    timeout=timeout or Config.SPEECH_TIMEOUT,
                    phrase_time_limit=phrase_timeout or Config.PHRASE_TIMEOUT
                )
            
            logging.info("Processing speech...")
            text = self.recognizer.recognize_google(audio)
            logging.info(f"Recognized text: {text}")
            return text
            
        except sr.WaitTimeoutError:
            logging.warning("No speech detected within timeout period")
            return None
        except sr.UnknownValueError:
            logging.warning("Could not understand the audio")
            return None
        except sr.RequestError as e:
            logging.error(f"Error with speech recognition service: {e}")
            return None
        except Exception as e:
            logging.error(f"Unexpected error in speech recognition: {e}")
            return None
    
    def test_microphone(self):
        """Test if microphone is working"""
        try:
            with self.microphone as source:
                self.recognizer.adjust_for_ambient_noise(source, duration=1)
            return True
        except Exception as e:
            logging.error(f"Microphone test failed: {e}")
            return False
