# Voice Assistant App

A Python-based voice assistant that uses speech recognition, Google Gemini AI, and text-to-speech to create an interactive AI conversation experience.

## Features

- 🎤 **Speech Recognition**: Uses Google Speech Recognition to convert speech to text
- 🤖 **AI Integration**: Powered by Google Gemini for intelligent responses
- 🔊 **Text-to-Speech**: Uses Google Text-to-Speech (gTTS) to speak responses
- 💬 **Conversation History**: Maintains context across the conversation
- 🖥️ **User-Friendly GUI**: Simple tkinter interface with visual feedback
- 🔧 **Service Testing**: Built-in testing for all components

## Prerequisites

- Python 3.7 or higher
- Microphone and speakers/headphones
- Google Gemini API key
- Internet connection (required for speech recognition, Gemini AI, and TTS)

## Installation

1. **Clone or download this repository**

2. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up your Google Gemini API key**:
   - Copy `.env.example` to `.env`
   - Edit `.env` and add your Gemini API key:
     ```
     GEMINI_API_KEY=your_actual_api_key_here
     ```
   - Get your API key from: https://aistudio.google.com/app/apikey

4. **Install PyAudio** (if you encounter issues):
   - **Windows**: PyAudio should install automatically with pip
   - **macOS**: `brew install portaudio` then `pip install pyaudio`
   - **Linux**: `sudo apt-get install python3-pyaudio` or `sudo apt-get install portaudio19-dev` then `pip install pyaudio`

## Usage

1. **Run the application**:
   ```bash
   python main.py
   ```

2. **First-time setup**:
   - Click "🔧 Test Services" to verify all components are working
   - Make sure your microphone permissions are enabled

3. **Using the voice assistant**:
   - Click "🎤 Start Listening" to begin voice input
   - Speak your question or message
   - The app will process your speech, send it to Gemini AI, and speak the response
   - Use "⏹ Stop Speaking" to interrupt the AI's response
   - Use "🗑 Clear Chat" to start a new conversation

## Configuration

You can modify settings in `config.py`:

- **Audio settings**: Sample rate, chunk size, timeout values
- **Gemini settings**: Model, max tokens, temperature
- **TTS settings**: Language, speech speed
- **UI settings**: Window size, title

## Troubleshooting

### Common Issues

1. **"No module named 'pyaudio'"**:
   - Install PyAudio using the platform-specific instructions above

2. **"Authentication failed"**:
   - Check that your Gemini API key is correctly set in the `.env` file
   - Verify your API key is valid and active

3. **Microphone not working**:
   - Check microphone permissions in your system settings
   - Test with other applications to ensure microphone is functional
   - Try running the service test in the app

4. **No audio output**:
   - Check system volume and audio output device
   - Ensure pygame and gTTS are properly installed

5. **Speech recognition not working**:
   - Ensure you have an internet connection
   - Speak clearly and avoid background noise
   - Check microphone sensitivity settings

### Logs

The application creates a log file `voice_assistant.log` with detailed information about operations and any errors.

## API Costs

This application uses the following services:
- **Google Gemini API**: Free tier available with generous limits, then paid
- **Google Speech Recognition**: Free tier available, then paid
- **Google Text-to-Speech**: Free tier available, then paid

Monitor your usage to avoid unexpected charges. Gemini has a very generous free tier that should be sufficient for most personal use.

## License

This project is open source and available under the MIT License.

## Contributing

Feel free to submit issues, feature requests, or pull requests to improve the application.
