import openai
import logging
from config import Config

class ChatGPTService:
    def __init__(self):
        openai.api_key = Config.OPENAI_API_KEY
        self.conversation_history = []
        logging.info("ChatGPT service initialized")
    
    def get_response(self, user_input):
        """
        Get response from ChatGPT
        
        Args:
            user_input (str): User's message
            
        Returns:
            str: ChatGPT's response or error message
        """
        try:
            # Add user message to conversation history
            self.conversation_history.append({"role": "user", "content": user_input})
            
            # Keep conversation history manageable (last 10 messages)
            if len(self.conversation_history) > 10:
                self.conversation_history = self.conversation_history[-10:]
            
            # Create chat completion
            response = openai.chat.completions.create(
                model=Config.CHATGPT_MODEL,
                messages=self.conversation_history,
                max_tokens=Config.MAX_TOKENS,
                temperature=Config.TEMPERATURE
            )
            
            # Extract response text
            assistant_response = response.choices[0].message.content.strip()
            
            # Add assistant response to conversation history
            self.conversation_history.append({"role": "assistant", "content": assistant_response})
            
            logging.info(f"ChatGPT response: {assistant_response}")
            return assistant_response
            
        except openai.AuthenticationError:
            error_msg = "Authentication failed. Please check your OpenAI API key."
            logging.error(error_msg)
            return error_msg
        except openai.RateLimitError:
            error_msg = "Rate limit exceeded. Please try again later."
            logging.error(error_msg)
            return error_msg
        except openai.APIError as e:
            error_msg = f"OpenAI API error: {e}"
            logging.error(error_msg)
            return error_msg
        except Exception as e:
            error_msg = f"Unexpected error: {e}"
            logging.error(error_msg)
            return error_msg
    
    def clear_conversation(self):
        """Clear conversation history"""
        self.conversation_history = []
        logging.info("Conversation history cleared")
    
    def get_conversation_history(self):
        """Get current conversation history"""
        return self.conversation_history.copy()
