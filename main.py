import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import logging
from datetime import datetime

from config import Config
from speech_recognition_service import SpeechRecognitionService
from chatgpt_service import ChatGPTService
from text_to_speech_service import TextToSpeechService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('voice_assistant.log'),
        logging.StreamHandler()
    ]
)

class VoiceAssistantApp:
    def __init__(self, root):
        self.root = root
        self.root.title(Config.WINDOW_TITLE)
        self.root.geometry(Config.WINDOW_SIZE)
        
        # Initialize services
        try:
            Config.validate_config()
            self.speech_service = SpeechRecognitionService()
            self.chatgpt_service = ChatGPTService()
            self.tts_service = TextToSpeechService()
        except ValueError as e:
            messagebox.showerror("Configuration Error", str(e))
            self.root.destroy()
            return
        
        # State variables
        self.is_listening = False
        self.is_speaking = False
        
        self.setup_ui()
        self.log_message("Voice Assistant initialized successfully!")
    
    def setup_ui(self):
        """Setup the user interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="Voice Assistant", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 10))
        
        # Chat display
        chat_frame = ttk.LabelFrame(main_frame, text="Conversation", padding="5")
        chat_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        chat_frame.columnconfigure(0, weight=1)
        chat_frame.rowconfigure(0, weight=1)
        
        self.chat_display = scrolledtext.ScrolledText(
            chat_frame, 
            wrap=tk.WORD, 
            width=60, 
            height=15,
            state=tk.DISABLED
        )
        self.chat_display.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Control buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, columnspan=3, pady=(0, 10))
        
        self.listen_button = ttk.Button(
            button_frame, 
            text="🎤 Start Listening", 
            command=self.toggle_listening
        )
        self.listen_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.stop_button = ttk.Button(
            button_frame, 
            text="⏹ Stop Speaking", 
            command=self.stop_speaking
        )
        self.stop_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.clear_button = ttk.Button(
            button_frame, 
            text="🗑 Clear Chat", 
            command=self.clear_conversation
        )
        self.clear_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.test_button = ttk.Button(
            button_frame, 
            text="🔧 Test Services", 
            command=self.test_services
        )
        self.test_button.pack(side=tk.LEFT)
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E))
    
    def log_message(self, message, sender="System"):
        """Add message to chat display"""
        self.chat_display.config(state=tk.NORMAL)
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.chat_display.insert(tk.END, f"[{timestamp}] {sender}: {message}\n\n")
        self.chat_display.see(tk.END)
        self.chat_display.config(state=tk.DISABLED)
    
    def update_status(self, status):
        """Update status bar"""
        self.status_var.set(status)
        self.root.update_idletasks()
    
    def toggle_listening(self):
        """Toggle listening state"""
        if self.is_listening:
            self.stop_listening()
        else:
            self.start_listening()
    
    def start_listening(self):
        """Start listening for speech"""
        if self.is_listening:
            return
        
        self.is_listening = True
        self.listen_button.config(text="🔴 Listening...", state=tk.DISABLED)
        self.update_status("Listening for speech...")
        
        # Start listening in a separate thread
        threading.Thread(target=self._listen_thread, daemon=True).start()
    
    def stop_listening(self):
        """Stop listening for speech"""
        self.is_listening = False
        self.listen_button.config(text="🎤 Start Listening", state=tk.NORMAL)
        self.update_status("Ready")
    
    def _listen_thread(self):
        """Thread function for listening to speech"""
        try:
            text = self.speech_service.listen_for_speech()
            
            if text and self.is_listening:
                self.root.after(0, self._process_speech, text)
            else:
                self.root.after(0, self.stop_listening)
                
        except Exception as e:
            logging.error(f"Error in listening thread: {e}")
            self.root.after(0, self.stop_listening)
    
    def _process_speech(self, text):
        """Process recognized speech"""
        self.log_message(text, "You")
        self.update_status("Getting AI response...")
        
        # Get ChatGPT response in a separate thread
        threading.Thread(target=self._get_ai_response, args=(text,), daemon=True).start()
        
        self.stop_listening()
    
    def _get_ai_response(self, text):
        """Get response from ChatGPT"""
        try:
            response = self.chatgpt_service.get_response(text)
            self.root.after(0, self._speak_response, response)
        except Exception as e:
            logging.error(f"Error getting AI response: {e}")
            self.root.after(0, self.update_status, "Error getting AI response")
    
    def _speak_response(self, response):
        """Speak the AI response"""
        self.log_message(response, "Assistant")
        self.update_status("Speaking response...")
        
        # Speak in a separate thread
        threading.Thread(target=self._speak_thread, args=(response,), daemon=True).start()
    
    def _speak_thread(self, text):
        """Thread function for speaking text"""
        try:
            self.is_speaking = True
            success = self.tts_service.speak(text)
            self.is_speaking = False
            
            if success:
                self.root.after(0, self.update_status, "Ready")
            else:
                self.root.after(0, self.update_status, "Error in speech synthesis")
                
        except Exception as e:
            logging.error(f"Error in speaking thread: {e}")
            self.is_speaking = False
            self.root.after(0, self.update_status, "Error in speech synthesis")
    
    def stop_speaking(self):
        """Stop current speech"""
        self.tts_service.stop_speaking()
        self.is_speaking = False
        self.update_status("Speech stopped")
    
    def clear_conversation(self):
        """Clear conversation history"""
        self.chatgpt_service.clear_conversation()
        self.chat_display.config(state=tk.NORMAL)
        self.chat_display.delete(1.0, tk.END)
        self.chat_display.config(state=tk.DISABLED)
        self.log_message("Conversation cleared")
    
    def test_services(self):
        """Test all services"""
        self.update_status("Testing services...")
        threading.Thread(target=self._test_services_thread, daemon=True).start()
    
    def _test_services_thread(self):
        """Thread function for testing services"""
        try:
            # Test microphone
            mic_ok = self.speech_service.test_microphone()
            self.root.after(0, self.log_message, f"Microphone test: {'✓ OK' if mic_ok else '✗ Failed'}")
            
            # Test TTS
            tts_ok = self.tts_service.test_tts()
            self.root.after(0, self.log_message, f"Text-to-speech test: {'✓ OK' if tts_ok else '✗ Failed'}")
            
            # Test ChatGPT
            chatgpt_response = self.chatgpt_service.get_response("Hello, this is a test.")
            chatgpt_ok = not chatgpt_response.startswith("Error") and not chatgpt_response.startswith("Authentication")
            self.root.after(0, self.log_message, f"ChatGPT test: {'✓ OK' if chatgpt_ok else '✗ Failed'}")
            
            self.root.after(0, self.update_status, "Service tests completed")
            
        except Exception as e:
            logging.error(f"Error testing services: {e}")
            self.root.after(0, self.update_status, "Error testing services")

def main():
    root = tk.Tk()
    app = VoiceAssistantApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
