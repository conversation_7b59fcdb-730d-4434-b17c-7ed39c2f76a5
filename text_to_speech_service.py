import os
import tempfile
import pygame
from gtts import gTTS
import logging
from config import Config

class TextToSpeechService:
    def __init__(self):
        pygame.mixer.init()
        self.temp_dir = tempfile.gettempdir()
        logging.info("Text-to-speech service initialized")
    
    def speak(self, text):
        """
        Convert text to speech and play it
        
        Args:
            text (str): Text to convert to speech
            
        Returns:
            bool: True if successful, False otherwise
        """
        if not text or not text.strip():
            logging.warning("Empty text provided for speech")
            return False
        
        try:
            # Create gTTS object
            tts = gTTS(
                text=text,
                lang=Config.TTS_LANGUAGE,
                slow=Config.TTS_SLOW
            )
            
            # Create temporary file
            temp_file = os.path.join(self.temp_dir, "tts_output.mp3")
            
            # Save to temporary file
            tts.save(temp_file)
            
            # Play the audio file
            pygame.mixer.music.load(temp_file)
            pygame.mixer.music.play()
            
            # Wait for playback to complete
            while pygame.mixer.music.get_busy():
                pygame.time.wait(100)
            
            # Clean up temporary file
            try:
                os.remove(temp_file)
            except:
                pass  # Ignore cleanup errors
            
            logging.info(f"Successfully spoke text: {text[:50]}...")
            return True
            
        except Exception as e:
            logging.error(f"Error in text-to-speech: {e}")
            return False
    
    def stop_speaking(self):
        """Stop current speech playback"""
        try:
            pygame.mixer.music.stop()
            logging.info("Speech playback stopped")
        except Exception as e:
            logging.error(f"Error stopping speech: {e}")
    
    def is_speaking(self):
        """Check if currently speaking"""
        return pygame.mixer.music.get_busy()
    
    def test_tts(self):
        """Test text-to-speech functionality"""
        try:
            test_text = "Text to speech is working correctly."
            return self.speak(test_text)
        except Exception as e:
            logging.error(f"TTS test failed: {e}")
            return False
