import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Config:
    # Google Gemini API Configuration
    GEMINI_API_KEY = os.getenv('GEMINI_API_KEY', '')

    # Audio Configuration
    SAMPLE_RATE = 16000
    CHUNK_SIZE = 1024
    AUDIO_FORMAT = 'wav'
    CHANNELS = 1

    # Speech Recognition Configuration
    SPEECH_TIMEOUT = 5  # seconds
    PHRASE_TIMEOUT = 1  # seconds

    # Text-to-Speech Configuration
    TTS_LANGUAGE = 'en'
    TTS_SLOW = False

    # Gemini Configuration
    GEMINI_MODEL = 'gemini-1.5-flash'
    MAX_TOKENS = 150
    TEMPERATURE = 0.7

    # Application Configuration
    WINDOW_TITLE = "Voice Assistant (Gemini)"
    WINDOW_SIZE = "600x500"

    @classmethod
    def validate_config(cls):
        """Validate that required configuration is present"""
        if not cls.GEMINI_API_KEY:
            raise ValueError("GEMINI_API_KEY is required. Please set it in your .env file.")
        return True
