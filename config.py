import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Config:
    # OpenAI API Configuration
    OPENAI_API_KEY = os.getenv('OPENAI_API_KEY', '')
    
    # Audio Configuration
    SAMPLE_RATE = 16000
    CHUNK_SIZE = 1024
    AUDIO_FORMAT = 'wav'
    CHANNELS = 1
    
    # Speech Recognition Configuration
    SPEECH_TIMEOUT = 5  # seconds
    PHRASE_TIMEOUT = 1  # seconds
    
    # Text-to-Speech Configuration
    TTS_LANGUAGE = 'en'
    TTS_SLOW = False
    
    # ChatGPT Configuration
    CHATGPT_MODEL = 'gpt-3.5-turbo'
    MAX_TOKENS = 150
    TEMPERATURE = 0.7
    
    # Application Configuration
    WINDOW_TITLE = "Voice Assistant"
    WINDOW_SIZE = "600x500"
    
    @classmethod
    def validate_config(cls):
        """Validate that required configuration is present"""
        if not cls.OPENAI_API_KEY:
            raise ValueError("OPENAI_API_KEY is required. Please set it in your .env file.")
        return True
